'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaReact, FaNodeJs, FaFigma, FaGithub, FaCode, FaBriefcase } from 'react-icons/fa';
import { SiNextdotjs, SiTailwindcss, SiTypescript, SiFirebase, SiFramer, SiVercel } from 'react-icons/si';

const ExperienceToolsSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });

  const experiences = [
    {
      title: 'Senior Frontend Developer',
      company: 'Tech Innovations Inc.',
      period: '2023 - Present',
      description: 'Led the development of responsive web applications using Next.js and TypeScript. Implemented state management with Zustand and Redux.'
    },
    {
      title: 'Web Developer',
      company: 'Digital Solutions',
      period: '2021 - 2023',
      description: 'Developed and maintained client websites. Created reusable components and implemented responsive designs.'
    },
    {
      title: 'Junior Developer',
      company: 'Creative Agency',
      period: '2019 - 2021',
      description: 'Assisted in building web applications. Collaborated with designers to implement pixel-perfect UIs.'
    }
  ];

  const tools = [
    { name: 'React', icon: <FaReact size={28} />, color: '#61DAFB' },
    { name: 'Next.js', icon: <SiNextdotjs size={28} />, color: '#ffffff' },
    { name: 'TypeScript', icon: <SiTypescript size={28} />, color: '#3178C6' },
    { name: 'Node.js', icon: <FaNodeJs size={28} />, color: '#339933' },
    { name: 'Tailwind', icon: <SiTailwindcss size={28} />, color: '#06B6D4' },
    { name: 'Figma', icon: <FaFigma size={28} />, color: '#F24E1E' },
    { name: 'GitHub', icon: <FaGithub size={28} />, color: '#ffffff' },
    { name: 'Firebase', icon: <SiFirebase size={28} />, color: '#FFCA28' },
    { name: 'Framer', icon: <SiFramer size={28} />, color: '#0055FF' },
    { name: 'Vercel', icon: <SiVercel size={28} />, color: '#ffffff' }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  return (
    <section 
      id="experience" 
      ref={sectionRef} 
      className="py-24 relative overflow-hidden"
      style={{ background: 'var(--card-bg)' }}
    >
      {/* Background decorations */}
      <div className="absolute inset-0 -z-10 opacity-30">
        <div className="absolute top-0 right-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMwNmI2ZDQiIGZpbGwtb3BhY2l0eT0iMC4xIj48cGF0aCBkPSJNMzYgMzRjMC0yLjIwOS0xLjc5MS00LTQtNHMtNCAxLjc5MS00IDQgMS43OTEgNCA0IDQgNC0xLjc5MSA0LTRtMi0xMGMwLTIuMjA5LTEuNzkxLTQtNC00cy00IDEuNzkxLTQgNCAxLjc5MSA0IDQgNCA0LTEuNzkxIDQtNG0tMTYgMGMwLTIuMjA5LTEuNzkxLTQtNC00cy00IDEuNzkxLTQgNCAxLjc5MSA0IDQgNCA0LTEuNzkxIDQtNG0tMTYgMTBjMC0yLjIwOS0xLjc5MS00LTQtNHMtNCAxLjc5MS00IDQgMS43OTEgNCA0IDQgNC0xLjc5MSA0LTQiLz48L2c+PC9nPjwvc3ZnPg==')] opacity-10"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4 inline-block relative">
              <span className="text-foreground relative z-10">Experience & Skills</span>
              <span className="absolute bottom-0 left-0 w-full h-3 bg-[var(--accent-primary)] opacity-20 rounded"></span>
            </h2>
          </motion.div>
          <motion.p 
            className="text-foreground/70 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            A combination of industry experience and cutting-edge technical skills
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-12">
          {/* Experience Column - Takes 3/5 of the space */}
          <motion.div
            className="lg:col-span-3 relative"
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
          >
            <div className="flex items-center mb-8">
              <div className="w-12 h-12 rounded-xl glass flex items-center justify-center mr-4 neon-box">
                <FaBriefcase className="text-[var(--accent-primary)] text-xl" />
              </div>
              <h3 className="text-2xl font-bold text-foreground">Professional Journey</h3>
            </div>

            <div className="relative pl-8 space-y-12">
              {/* Timeline line */}
              <div className="absolute left-0 top-2 bottom-10 w-0.5 bg-gradient-to-b from-[var(--accent-primary)] to-[var(--accent-secondary)] opacity-50"></div>
              
              {experiences.map((exp, index) => (
                <motion.div 
                  key={index}
                  variants={itemVariants}
                  className="relative"
                >
                  {/* Timeline dot */}
                  <div className="absolute -left-8 top-0 w-4 h-4 rounded-full glass border border-[var(--accent-primary)] neon-box"></div>
                  
                  <div className="glass rounded-xl p-6 hover:shadow-lg transition-all border border-[rgba(255,255,255,0.05)] group">
                    <h3 className="text-xl font-semibold text-foreground group-hover:text-[var(--accent-primary)] transition-colors">{exp.title}</h3>
                    <div className="flex justify-between items-center mt-2 mb-3">
                      <p className="text-[var(--accent-secondary)]">{exp.company}</p>
                      <p className="text-foreground/50 text-sm px-3 py-1 rounded-full glass">{exp.period}</p>
                    </div>
                    <p className="text-foreground/80">{exp.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Tools Column - Takes 2/5 of the space */}
          <motion.div
            className="lg:col-span-2"
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            id="tools"
          >
            <div className="flex items-center mb-8">
              <div className="w-12 h-12 rounded-xl glass flex items-center justify-center mr-4 neon-box">
                <FaCode className="text-[var(--accent-tertiary)] text-xl" />
              </div>
              <h3 className="text-2xl font-bold text-foreground">Tech Stack</h3>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 gap-4">
              {tools.map((tool, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, boxShadow: '0 0 15px rgba(6, 182, 212, 0.2)' }}
                  className="glass rounded-xl p-4 flex flex-col items-center justify-center border border-[rgba(255,255,255,0.05)] group"
                >
                  <div 
                    className="w-16 h-16 rounded-full glass flex items-center justify-center mb-3 group-hover:neon-box transition-all"
                    style={{ color: tool.color }}
                  >
                    {tool.icon}
                  </div>
                  <p className="text-foreground/90 group-hover:text-[var(--accent-primary)] transition-colors">{tool.name}</p>
                </motion.div>
              ))}
            </div>

            {/* Skill meter */}
            <motion.div 
              className="mt-12 space-y-6"
              variants={containerVariants}
              initial="hidden"
              animate={isInView ? "visible" : "hidden"}
            >
              <h3 className="text-xl font-bold text-foreground mb-4">Proficiency</h3>
              
              {[
                { name: 'Frontend Development', level: 95 },
                { name: 'UI/UX Design', level: 85 },
                { name: 'Backend Development', level: 75 },
                { name: 'DevOps', level: 65 }
              ].map((skill, index) => (
                <motion.div key={index} variants={itemVariants} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-foreground/80">{skill.name}</span>
                    <span className="text-[var(--accent-primary)]">{skill.level}%</span>
                  </div>
                  <div className="h-2 w-full bg-[rgba(255,255,255,0.1)] rounded-full overflow-hidden">
                    <motion.div 
                      className="h-full gradient-bg"
                      initial={{ width: 0 }}
                      animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                      transition={{ duration: 1, delay: 0.2 + (index * 0.1) }}
                    />
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ExperienceToolsSection;
