@import "tailwindcss";

:root {
  --background: #121212; /* Dark black background */
  --foreground: #f8fafc;
  --accent-primary: #ff5722; /* Orange */
  --accent-secondary: #ff8a65; /* Light Orange */
  --accent-tertiary: #e64a19; /* Dark Orange */
  --card-bg: #1e1e1e;
  --card-hover: #2d2d2d;
}

/* Theme variables */
:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: light) {
  :root {
    --background: #f8fafc;
    --foreground: #0f172a;
    --card-bg: #ffffff;
    --card-hover: #f1f5f9;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--accent-primary), var(--accent-secondary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #e64a19, #ff8a65);
}

/* Light mode scrollbar */
@media (prefers-color-scheme: light) {
  ::-webkit-scrollbar-track {
    background: #e2e8f0;
  }
}

/* Neon glow effects */
.neon-glow {
  text-shadow: 0 0 5px rgba(255, 87, 34, 0.7), 0 0 10px rgba(255, 87, 34, 0.5), 0 0 15px rgba(255, 87, 34, 0.3);
}

.neon-box {
  box-shadow: 0 0 5px rgba(255, 87, 34, 0.7), 0 0 10px rgba(255, 87, 34, 0.5), 0 0 15px rgba(255, 87, 34, 0.3);
}

.neon-border {
  position: relative;
}

.neon-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
  z-index: -1;
  border-radius: inherit;
  animation: border-animation 3s linear infinite;
}

@keyframes border-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Glass morphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animated gradient background */
.gradient-bg {
  background: linear-gradient(-45deg, #ff5722, #ff8a65, #e64a19, #ff7043);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating animations for cubes */
@keyframes float-slow {
  0%, 100% { transform: translateY(0) rotate(12deg); }
  50% { transform: translateY(-20px) rotate(15deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0) rotate(-12deg); }
  50% { transform: translateY(-15px) rotate(-15deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0) rotate(45deg); }
  50% { transform: translateY(-10px) rotate(40deg); }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 3s ease-in-out infinite;
}

/* Line clamp utility for review text */
.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Review card hover effects */
.review-card {
  transition: all 0.3s ease;
}

.review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 87, 34, 0.1);
}
