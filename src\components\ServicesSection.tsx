'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaCheck, FaMobileAlt, FaDesktop, FaTabletAlt, FaRocket, FaLaptopCode, FaPalette, FaSearchDollar } from 'react-icons/fa';

const ServicesSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });

  const services = [
    {
      icon: <FaLaptopCode />,
      title: 'Web Development',
      description: 'Modern, responsive websites built with cutting-edge technologies that deliver exceptional user experiences.',
      color: 'var(--accent-primary)'
    },
    {
      icon: <FaPalette />,
      title: 'UI/UX Design',
      description: 'Intuitive and visually stunning interfaces that engage users and enhance brand perception.',
      color: 'var(--accent-secondary)'
    },
    {
      icon: <FaRocket />,
      title: 'Performance Optimization',
      description: 'Lightning-fast websites optimized for speed, SEO, and conversion to maximize your online potential.',
      color: 'var(--accent-tertiary)'
    },
    {
      icon: <FaSearchDollar />,
      title: 'SEO & Analytics',
      description: 'Data-driven strategies to improve visibility, track performance and drive meaningful business results.',
      color: '#f59e0b' // Amber
    }
  ];

  const benefits = [
    'Clean, modern, and responsive designs',
    'Fast-loading and optimized websites',
    'SEO-friendly development',
    'Accessible user interfaces',
    'Collaborative approach to projects'
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  return (
    <section 
      id="about" 
      ref={sectionRef} 
      className="py-24 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-0 left-0 w-full h-full gradient-bg opacity-10"></div>
        <div className="absolute top-0 right-0 w-1/2 h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wNSI+PHBhdGggZD0iTTM2IDM0YzAtMi4yMDktMS43OTEtNC00LTRzLTQgMS43OTEtNCA0IDEuNzkxIDQgNCA0IDQtMS43OTEgNC00bTItMTBjMC0yLjIwOS0xLjc5MS00LTQtNHMtNCAxLjc5MS00IDQgMS43OTEgNCA0IDQgNC0xLjc5MSA0LTRtLTE2IDBjMC0yLjIwOS0xLjc5MS00LTQtNHMtNCAxLjc5MS00IDQgMS43OTEgNCA0IDQgNC0xLjc5MSA0LTRtLTE2IDEwYzAtMi4yMDktMS43OTEtNC00LTRzLTQgMS43OTEtNCA0IDEuNzkxIDQgNCA0IDQtMS43OTEgNC00Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-20"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4 inline-block relative">
              <span className="text-foreground relative z-10">Why Choose Me</span>
              <span className="absolute bottom-0 left-0 w-full h-3 bg-[var(--accent-secondary)] opacity-20 rounded"></span>
            </h2>
          </motion.div>
          <motion.p 
            className="text-foreground/70 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Delivering exceptional digital experiences through innovative solutions
          </motion.p>
        </div>

        {/* Services Grid */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -10, boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)' }}
              className="glass rounded-xl p-6 border border-[rgba(255,255,255,0.05)] group transition-all duration-300"
            >
              <div 
                className="w-16 h-16 rounded-lg glass flex items-center justify-center mb-5 group-hover:neon-box transition-all"
                style={{ color: service.color }}
              >
                <span className="text-3xl">{service.icon}</span>
              </div>
              <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-[var(--accent-primary)] transition-colors">
                {service.title}
              </h3>
              <p className="text-foreground/70">
                {service.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Text Content */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
          >
            <h3 className="text-2xl font-bold mb-6 text-foreground">Exceptional Quality & Results</h3>
            <p className="text-foreground/70 mb-8">
              I specialize in creating beautiful, functional, and user-friendly websites that help businesses achieve their goals. 
              With a focus on modern technologies and best practices, I deliver solutions that stand out in today's competitive digital landscape.
            </p>
            
            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <motion.div 
                  key={index}
                  variants={itemVariants}
                  className="flex items-start"
                >
                  <span className="flex-shrink-0 w-6 h-6 rounded-full gradient-bg flex items-center justify-center mr-3 mt-1 neon-box">
                    <FaCheck className="text-white text-xs" />
                  </span>
                  <p className="text-foreground/80">{benefit}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Side - 3D Device Mockups */}
          <div className="relative h-[400px] flex items-center justify-center">
            {/* Floating circle decorations */}
            <motion.div 
              className="absolute w-20 h-20 rounded-full bg-[var(--accent-primary)] opacity-10 blur-md"
              animate={{ 
                x: [0, 20, 0], 
                y: [0, -20, 0],
                scale: [1, 1.2, 1] 
              }}
              transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
              style={{ top: '10%', left: '20%' }}
            />
            <motion.div 
              className="absolute w-16 h-16 rounded-full bg-[var(--accent-secondary)] opacity-10 blur-md"
              animate={{ 
                x: [0, -20, 0], 
                y: [0, 20, 0],
                scale: [1.2, 1, 1.2] 
              }}
              transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
              style={{ bottom: '15%', right: '25%' }}
            />

            {/* Desktop Mockup */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={isInView ? { y: 0, opacity: 1 } : { y: 20, opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="absolute left-0 bottom-0 glass rounded-lg overflow-hidden w-48 h-32 md:w-64 md:h-40 z-10 border border-[rgba(255,255,255,0.1)] neon-box"
            >
              <div className="h-4 bg-[rgba(0,0,0,0.2)] flex items-center px-2">
                <div className="w-2 h-2 rounded-full bg-red-500 mr-1"></div>
                <div className="w-2 h-2 rounded-full bg-yellow-500 mr-1"></div>
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
              </div>
              <div className="gradient-bg h-full flex items-center justify-center">
                <FaDesktop className="text-white text-2xl" />
              </div>
            </motion.div>

            {/* Tablet Mockup */}
            <motion.div
              initial={{ y: -20, opacity: 0 }}
              animate={isInView ? { y: 0, opacity: 1 } : { y: -20, opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="absolute top-0 right-0 glass rounded-lg overflow-hidden w-32 h-40 md:w-40 md:h-56 z-20 border border-[rgba(255,255,255,0.1)] neon-box"
            >
              <div className="h-2 bg-[rgba(0,0,0,0.2)]"></div>
              <div className="gradient-bg h-full flex items-center justify-center">
                <FaTabletAlt className="text-white text-2xl" />
              </div>
            </motion.div>

            {/* Mobile Mockup */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 glass rounded-lg overflow-hidden w-24 h-40 md:w-32 md:h-56 z-30 border border-[rgba(255,255,255,0.1)] neon-box"
            >
              <div className="h-2 bg-[rgba(0,0,0,0.2)] flex justify-center">
                <div className="w-8 h-1 rounded-full bg-[rgba(255,255,255,0.2)]"></div>
              </div>
              <div className="gradient-bg h-full flex items-center justify-center">
                <FaMobileAlt className="text-white text-2xl" />
              </div>
            </motion.div>

            {/* Rotating ring */}
            <motion.div 
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 rounded-full border border-[rgba(255,255,255,0.1)]"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
